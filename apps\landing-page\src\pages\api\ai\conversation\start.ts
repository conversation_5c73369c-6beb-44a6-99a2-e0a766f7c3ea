import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

interface StartConversationRequest {
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation';
  culturalContext?: {
    location?: string;
    dialect?: string;
  };
}

interface StartConversationResponse {
  success: boolean;
  message: string;
  data?: {
    sessionId: string;
    currentStep: string;
    messages: any[];
    extractedData: any;
    status: string;
    completionRate: number;
  };
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<StartConversationResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get user session
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user?.email) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { userRole, language, sessionType, culturalContext }: StartConversationRequest = req.body;

    // Validate required fields
    if (!userRole || !language) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userRole, language'
      });
    }

    // First, authenticate with the API server using NextAuth session
    const authResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3005'}/api/v1/auth/nextauth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: session.user.email,
        name: session.user.name,
        image: session.user.image,
        provider: 'google',
      }),
    });

    if (!authResponse.ok) {
      const authError = await authResponse.text();
      console.error('API authentication failed:', authError);
      return res.status(500).json({
        success: false,
        message: 'Authentication with API server failed',
        error: authError
      });
    }

    const authData = await authResponse.json();
    if (!authData.success || !authData.data?.tokens?.accessToken) {
      return res.status(500).json({
        success: false,
        message: 'Failed to get access token from API server',
        error: 'Invalid authentication response'
      });
    }

    // Enhanced cultural context for Syrian marketplace
    const enhancedCulturalContext = {
      location: culturalContext?.location || 'Syria',
      dialect: culturalContext?.dialect || 'Syrian',
      marketFocus: 'local_and_regional',
      ...culturalContext
    };

    // Now use the proper JWT token to start the enhanced AI conversation
    const apiResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3005'}/api/v1/ai/conversation/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.data.tokens.accessToken}`,
        'X-Cultural-Context': JSON.stringify(enhancedCulturalContext),
      },
      body: JSON.stringify({
        userRole,
        language,
        sessionType: sessionType || 'onboarding',
        culturalContext: enhancedCulturalContext,
        userData: req.body.userData || {}
      }),
    });

    if (!apiResponse.ok) {
      const errorData = await apiResponse.json().catch(() => ({}));
      throw new Error(errorData.message || `API request failed with status ${apiResponse.status}`);
    }

    const apiData = await apiResponse.json();

    if (!apiData.success) {
      throw new Error(apiData.message || 'Failed to start conversation');
    }

    // Generate fallback welcome message if needed
    const fallbackWelcomeMessage = generateEnhancedWelcomeMessage(userRole, language, enhancedCulturalContext);

    // Return enhanced response with proper structure
    return res.status(201).json({
      success: true,
      message: 'Enhanced AI conversation started successfully',
      data: {
        id: apiData.data.sessionId,
        sessionId: apiData.data.sessionId,
        currentStep: apiData.data.currentStep || 'welcome',
        userRole: userRole,
        language: language,
        messages: apiData.data.messages || [
          {
            id: `welcome_${Date.now()}`,
            role: 'assistant',
            content: fallbackWelcomeMessage,
            timestamp: new Date().toISOString(),
            confidence: 1.0
          }
        ],
        extractedData: apiData.data.extractedData || {},
        status: apiData.data.status || 'active',
        completionRate: apiData.data.completionRate || 0.0,
        culturalContext: apiData.data.culturalContext || enhancedCulturalContext,
        recommendations: apiData.data.recommendations || []
      }
    });

  } catch (error: any) {
    console.error('Error starting AI conversation:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}

/**
 * Generate enhanced welcome message with cultural context
 */
function generateEnhancedWelcomeMessage(
  userRole: 'CLIENT' | 'EXPERT',
  language: 'ar' | 'en',
  culturalContext: any
): string {
  const location = culturalContext.location || 'Syria';

  if (language === 'ar') {
    if (userRole === 'EXPERT') {
      return `🇸🇾 أهلاً وسهلاً بك في فريلا سوريا!

أنا مساعدك الذكي المتخصص في السوق السوري، وسأساعدك في بناء ملف مهني قوي يجذب العملاء.

🎯 **هدفنا اليوم**: تحويل خبراتك إلى خدمات مربحة في السوق السوري والعربي

**سأساعدك في**:
✅ إنشاء ملف شخصي احترافي
✅ تحديد أسعار تنافسية مناسبة لـ${location}
✅ كتابة أوصاف خدمات جذابة
✅ بناء استراتيجية تسويق فعالة

**لنبدأ بالتعرف عليك**: ما هو مجال تخصصك الرئيسي؟

يمكنك أن تخبرني عن مهاراتك، خبراتك، أو أي مشاريع عملت عليها من قبل.

*جميع المعلومات آمنة ومحمية* 🔒`;
    } else {
      return `🇸🇾 أهلاً وسهلاً بك في فريلا سوريا!

أنا مساعدك الذكي المتخصص في السوق السوري، وسأساعدك في إيجاد أفضل الخبراء لمشروعك.

🎯 **هدفنا اليوم**: فهم احتياجاتك وربطك بالخبراء المناسبين في ${location}

**سأساعدك في**:
✅ تحديد نوع الخدمة المطلوبة
✅ وضع ميزانية وجدول زمني واقعي
✅ اختيار الخبير المناسب
✅ إدارة مشروعك بنجاح

**لنبدأ بسؤال بسيط**: ما نوع المشروع الذي تريد تنفيذه؟

يمكنك أن تخبرني عن فكرتك، متطلباتك، أو أي تفاصيل أخرى.

*جميع المعلومات آمنة ومحمية* 🔒`;
    }
  } else {
    if (userRole === 'EXPERT') {
      return `🇸🇾 Welcome to Freela Syria!

I'm your AI assistant specialized in the Syrian market, and I'll help you build a strong professional profile that attracts clients.

🎯 **Today's Goal**: Transform your expertise into profitable services in the Syrian and Arab markets

**I'll help you with**:
✅ Creating a professional profile
✅ Setting competitive prices suitable for ${location}
✅ Writing attractive service descriptions
✅ Building an effective marketing strategy

**Let's get to know you**: What is your main area of expertise?

You can tell me about your skills, experience, or any projects you've worked on before.

*All information is secure and protected* 🔒`;
    } else {
      return `🇸🇾 Welcome to Freela Syria!

I'm your AI assistant specialized in the Syrian market, and I'll help you find the best experts for your project.

🎯 **Today's Goal**: Understand your needs and connect you with suitable experts in ${location}

**I'll help you with**:
✅ Identifying the required service type
✅ Setting a realistic budget and timeline
✅ Choosing the right expert
✅ Managing your project successfully

**Let's start with a simple question**: What type of project do you want to implement?

You can tell me about your idea, requirements, or any other details.

*All information is secure and protected* 🔒`;
    }
  }
}
