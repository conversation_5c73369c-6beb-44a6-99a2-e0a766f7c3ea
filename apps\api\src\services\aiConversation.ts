/**
 * Enhanced AI Conversation Service
 * Manages AI-powered onboarding conversations for Freela Syria
 * Optimized for Syrian marketplace with Arabic RTL support and cultural context
 */

import { openRouterService, ChatMessage, ConversationContext, EnhancedAIResponse } from './openrouter';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';
import { nanoid } from 'nanoid';
import { supabase, supabaseAdmin } from '@freela/database/src/supabase';
import { profileCreationService, AIExtractedData } from './profileCreationService';

// Conversation flow steps for different user types
export const CONVERSATION_STEPS = {
  CLIENT: {
    WELCOME: 'welcome',
    PROJECT_TYPE: 'project_type',
    PROJECT_DETAILS: 'project_details',
    BUDGET_TIMELINE: 'budget_timeline',
    REQUIREMENTS: 'requirements',
    PREFERENCES: 'preferences',
    SUMMARY: 'summary',
    COMPLETION: 'completion',
  },
  EXPERT: {
    WELCOME: 'welcome',
    SKILLS_EXPERIENCE: 'skills_experience',
    SERVICES_OFFERED: 'services_offered',
    PRICING_STRATEGY: 'pricing_strategy',
    PORTFOLIO: 'portfolio',
    AVAILABILITY: 'availability',
    MARKET_POSITIONING: 'market_positioning',
    SUMMARY: 'summary',
    COMPLETION: 'completion',
  },
} as const;

export interface ConversationSession {
  id: string;
  userId: string;
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation';
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  currentStep: string;
  status: 'active' | 'completed' | 'abandoned' | 'paused';
  messages: ChatMessage[];
  extractedData: Record<string, any>;
  recommendations: any[];
  culturalContext: {
    location: string;
    dialect: string;
    marketFocus: string;
  };
  confidence: number;
  completionRate: number;
  createdAt: Date;
  lastActiveAt: Date;
}

export interface ConversationMessage {
  id: string;
  sessionId: string;
  role: 'system' | 'user' | 'assistant';
  content: string;
  contentArabic?: string;
  stepName?: string;
  intent?: string;
  confidence?: number;
  createdAt: Date;
}

class AIConversationService {
  private sessions: Map<string, ConversationSession> = new Map();

  /**
   * Start a new enhanced AI conversation session
   */
  async startConversation(params: {
    userId: string;
    userRole: 'CLIENT' | 'EXPERT';
    language: 'ar' | 'en';
    sessionType?: 'onboarding' | 'profile_optimization' | 'service_creation';
    culturalContext?: {
      location?: string;
      dialect?: string;
      marketFocus?: string;
    };
  }): Promise<ConversationSession> {
    const {
      userId,
      userRole,
      language,
      sessionType = 'onboarding',
      culturalContext = {
        location: 'Syria',
        dialect: 'Syrian',
        marketFocus: 'local_and_regional'
      }
    } = params;

    const sessionId = nanoid();
    const session: ConversationSession = {
      id: sessionId,
      userId,
      sessionType,
      userRole,
      language,
      currentStep: CONVERSATION_STEPS[userRole].WELCOME,
      status: 'active',
      messages: [],
      extractedData: {},
      recommendations: [],
      culturalContext,
      confidence: 0.0,
      completionRate: 0.0,
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    // Store session in memory (in production, this would be stored in database)
    this.sessions.set(sessionId, session);

    // Generate enhanced welcome message
    const welcomeMessage = await this.generateEnhancedWelcomeMessage(session);
    session.messages.push(welcomeMessage);

    // Update completion rate
    session.completionRate = this.calculateCompletionRate(session);

    logger.info('Enhanced AI conversation session started', {
      sessionId,
      userId,
      userRole,
      language,
      sessionType,
      culturalContext,
    });

    return session;
  }

  /**
   * Process user message and generate enhanced AI response
   */
  async processMessage(
    sessionId: string,
    userMessage: string
  ): Promise<{ session: ConversationSession; aiResponse: ChatMessage }> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw createError.notFound('Conversation session not found');
    }

    if (session.status !== 'active') {
      throw createError.badRequest('Conversation session is not active');
    }

    // Add user message to session
    const userChatMessage: ChatMessage = {
      role: 'user',
      content: userMessage,
      timestamp: new Date(),
    };
    session.messages.push(userChatMessage);

    // Build enhanced conversation context
    const context: ConversationContext = {
      userId: session.userId,
      sessionId: session.id,
      userRole: session.userRole,
      language: session.language,
      currentStep: session.currentStep,
      extractedData: session.extractedData,
      culturalContext: session.culturalContext,
      sessionType: session.sessionType,
    };

    try {
      // Get enhanced AI response with data extraction
      const enhancedResponse = await openRouterService.enhancedConversation(
        session.messages,
        context,
        {
          temperature: 0.7,
          maxTokens: 1000,
          extractData: true,
        }
      );

      // Create AI message
      const aiChatMessage: ChatMessage = {
        role: 'assistant',
        content: enhancedResponse.content,
        timestamp: new Date(),
      };

      // Add AI response to session
      session.messages.push(aiChatMessage);
      session.lastActiveAt = new Date();

      // Update session with extracted data
      if (enhancedResponse.extractedData) {
        session.extractedData = {
          ...session.extractedData,
          ...enhancedResponse.extractedData,
        };
      }

      // Update confidence and recommendations
      session.confidence = enhancedResponse.confidence;
      if (enhancedResponse.recommendations) {
        session.recommendations = enhancedResponse.recommendations;
      }

      // Update conversation step
      if (enhancedResponse.nextStep) {
        session.currentStep = enhancedResponse.nextStep;
      } else {
        await this.updateConversationStep(session);
      }

      // Update completion rate
      session.completionRate = this.calculateCompletionRate(session);

      // Check if conversation is completed
      if (session.completionRate >= 1.0) {
        session.status = 'completed';
        await this.handleConversationCompletion(session);
      }

      // Update session in storage
      this.sessions.set(sessionId, session);

      logger.info('Enhanced AI message processed', {
        sessionId,
        currentStep: session.currentStep,
        messageCount: session.messages.length,
        confidence: session.confidence,
        completionRate: session.completionRate,
        extractedDataKeys: Object.keys(session.extractedData),
      });

      return { session, aiResponse: aiChatMessage };

    } catch (error: any) {
      logger.error('Error processing enhanced AI message', {
        sessionId,
        error: error.message,
        userMessageLength: userMessage.length,
        currentStep: session.currentStep,
      });
      throw createError.internalServerError('Failed to process message');
    }
  }

  /**
   * Get conversation session
   */
  getSession(sessionId: string): ConversationSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Get user's active sessions
   */
  getUserSessions(userId: string): ConversationSession[] {
    return Array.from(this.sessions.values()).filter(
      session => session.userId === userId
    );
  }

  /**
   * Generate enhanced welcome message with cultural context
   */
  private async generateEnhancedWelcomeMessage(session: ConversationSession): Promise<ChatMessage> {
    const { userRole, language, culturalContext, sessionType } = session;

    const locationContext = culturalContext.location || 'سوريا';
    const sessionTypeText = sessionType === 'onboarding' ? 'إعداد الملف الشخصي' :
                           sessionType === 'profile_optimization' ? 'تحسين الملف الشخصي' :
                           'إنشاء خدمة جديدة';

    const welcomePrompts = {
      ar: {
        CLIENT: `🇸🇾 أهلاً وسهلاً بك في فريلا سوريا!

أنا مساعدك الذكي المتخصص في السوق السوري، وسأساعدك في ${sessionTypeText} وإيجاد أفضل الخبراء السوريين لمشروعك.

🎯 **هدفنا اليوم**: فهم احتياجاتك وربطك بالخبراء المناسبين في ${locationContext}

**لنبدأ بسؤال بسيط**: ما نوع المشروع الذي تريد تنفيذه؟

يمكنك أن تخبرني عن:
• 🎨 نوع الخدمة (تصميم، برمجة، كتابة، ترجمة، تسويق، إلخ)
• 📝 وصف مختصر للمشروع أو الفكرة
• 💰 الميزانية المتوقعة (إذا كانت محددة)
• ⏰ الجدول الزمني المطلوب
• 🎯 أي متطلبات خاصة

**لا تقلق** - سأرشدك خطوة بخطوة لضمان نجاح مشروعك! 😊

*ملاحظة: جميع المعلومات آمنة ومحمية وفقاً لمعايير الخصوصية*`,

        EXPERT: `🇸🇾 أهلاً وسهلاً بك في فريلا سوريا!

أنا مساعدك الذكي المتخصص في السوق السوري، وسأساعدك في ${sessionTypeText} وبناء حضور مهني قوي يجذب العملاء.

🎯 **هدفنا اليوم**: إبراز خبراتك السورية وتحويلها إلى خدمات مربحة في السوق المحلي والعربي

**لنبدأ بالتعرف عليك**: ما هي مهاراتك وخبراتك المهنية؟

يمكنك أن تخبرني عن:
• 💼 المجال الذي تعمل فيه (تقني، إبداعي، استشاري، إلخ)
• 📈 سنوات الخبرة ومستوى الإتقان
• 🛠️ أهم المهارات والأدوات التي تتقنها
• 🏆 مشاريع سابقة أو إنجازات مهمة
• 🎓 مؤهلات أو شهادات (إن وجدت)
• 💡 ما يميزك عن المنافسين

**سأساعدك في**:
✅ كتابة ملف شخصي احترافي
✅ تحديد أسعار تنافسية للسوق السوري
✅ إنشاء خدمات مطلوبة ومربحة
✅ بناء استراتيجية تسويق فعالة

**استعد لتحويل خبراتك إلى دخل مستدام!** 💪

*ملاحظة: جميع المعلومات آمنة ومحمية وفقاً لمعايير الخصوصية*`
      },
      en: {
        CLIENT: `Welcome to Freela Syria! 🎉

I'm your AI assistant and I'll help you set up your profile and find the best experts for your project.

Let's start with a simple question: What type of project do you want to work on?

You can tell me about:
• Type of service needed (design, programming, writing, translation, etc.)
• Brief project description
• Any other details you'd like to share

I'm here to help you every step of the way! 😊`,

        EXPERT: `Welcome to Freela Syria! 🌟

I'm your AI assistant and I'll help you build a strong professional profile and create outstanding services that attract clients.

Let's start by getting to know you better: What are your professional skills and experiences?

You can tell me about:
• Your field of work
• Years of experience
• Key skills you master
• Any previous projects you've worked on

I'll help you turn your expertise into profitable services! 💪`
      }
    };

    return {
      role: 'assistant',
      content: welcomePrompts[language][userRole],
      timestamp: new Date(),
    };
  }

  /**
   * Extract structured data from conversation messages
   */
  private async extractDataFromMessage(
    session: ConversationSession,
    userMessage: string,
    aiResponse: string
  ): Promise<void> {
    // This is a simplified version - in production, this would use more sophisticated NLP
    const { userRole, currentStep } = session;

    try {
      // Extract data based on current step and user role
      if (userRole === 'CLIENT') {
        await this.extractClientData(session, userMessage, currentStep);
      } else if (userRole === 'EXPERT') {
        await this.extractExpertData(session, userMessage, currentStep);
      }
    } catch (error: any) {
      logger.error('Error extracting data from message', {
        sessionId: session.id,
        currentStep,
        error: error.message,
      });
    }
  }

  /**
   * Extract data for client conversations
   */
  private async extractClientData(
    session: ConversationSession,
    userMessage: string,
    currentStep: string
  ): Promise<void> {
    const extractedData = session.extractedData;

    switch (currentStep) {
      case CONVERSATION_STEPS.CLIENT.PROJECT_TYPE:
        // Extract project type and category
        extractedData.projectType = this.extractProjectType(userMessage);
        break;

      case CONVERSATION_STEPS.CLIENT.BUDGET_TIMELINE:
        // Extract budget and timeline information
        const budgetInfo = this.extractBudgetInfo(userMessage);
        if (budgetInfo) {
          extractedData.budget = budgetInfo;
        }
        break;

      case CONVERSATION_STEPS.CLIENT.REQUIREMENTS:
        // Extract project requirements
        if (!extractedData.requirements) {
          extractedData.requirements = [];
        }
        extractedData.requirements.push(userMessage);
        break;
    }
  }

  /**
   * Extract data for expert conversations
   */
  private async extractExpertData(
    session: ConversationSession,
    userMessage: string,
    currentStep: string
  ): Promise<void> {
    const extractedData = session.extractedData;

    switch (currentStep) {
      case CONVERSATION_STEPS.EXPERT.SKILLS_EXPERIENCE:
        // Extract skills and experience
        extractedData.skills = this.extractSkills(userMessage);
        extractedData.experience = this.extractExperience(userMessage);
        break;

      case CONVERSATION_STEPS.EXPERT.SERVICES_OFFERED:
        // Extract service offerings
        if (!extractedData.services) {
          extractedData.services = [];
        }
        extractedData.services.push(userMessage);
        break;

      case CONVERSATION_STEPS.EXPERT.PRICING_STRATEGY:
        // Extract pricing information
        const pricingInfo = this.extractPricingInfo(userMessage);
        if (pricingInfo) {
          extractedData.pricing = pricingInfo;
        }
        break;
    }
  }

  /**
   * Update conversation step based on current progress
   */
  private async updateConversationStep(session: ConversationSession): Promise<void> {
    const { userRole, currentStep } = session;
    const steps = CONVERSATION_STEPS[userRole];
    const stepKeys = Object.values(steps) as string[];
    const currentIndex = stepKeys.indexOf(currentStep as string);

    // Move to next step if we have enough information
    if (currentIndex < stepKeys.length - 1) {
      session.currentStep = stepKeys[currentIndex + 1];
    } else {
      // Conversation completed
      session.status = 'completed';
    }
  }

  // Helper methods for data extraction (simplified versions)
  private extractProjectType(message: string): string {
    // Simple keyword matching - in production, use more sophisticated NLP
    const keywords = {
      'تصميم': 'design',
      'برمجة': 'programming',
      'كتابة': 'writing',
      'ترجمة': 'translation',
      'تسويق': 'marketing',
    };

    for (const [arabic, english] of Object.entries(keywords)) {
      if (message.includes(arabic) || message.toLowerCase().includes(english)) {
        return english;
      }
    }
    return 'other';
  }

  private extractBudgetInfo(message: string): any {
    // Extract budget information using regex
    const budgetRegex = /(\d+)\s*(دولار|dollar|\$|usd)/i;
    const match = message.match(budgetRegex);
    if (match) {
      return {
        amount: parseInt(match[1]),
        currency: 'USD',
      };
    }
    return null;
  }

  private extractSkills(message: string): string[] {
    // Extract skills from message
    const commonSkills = [
      'javascript', 'python', 'react', 'nodejs', 'php', 'html', 'css',
      'photoshop', 'illustrator', 'figma', 'wordpress', 'seo', 'marketing'
    ];

    return commonSkills.filter(skill => 
      message.toLowerCase().includes(skill)
    );
  }

  private extractExperience(message: string): any {
    // Extract years of experience
    const expRegex = /(\d+)\s*(سنة|سنوات|year|years)/i;
    const match = message.match(expRegex);
    if (match) {
      return {
        years: parseInt(match[1]),
        level: this.getExperienceLevel(parseInt(match[1])),
      };
    }
    return null;
  }

  private extractPricingInfo(message: string): any {
    // Extract pricing information
    const hourlyRegex = /(\d+)\s*(دولار|dollar|\$)\s*(ساعة|hour)/i;
    const match = message.match(hourlyRegex);
    if (match) {
      return {
        type: 'hourly',
        rate: parseInt(match[1]),
        currency: 'USD',
      };
    }
    return null;
  }

  private getExperienceLevel(years: number): string {
    if (years < 1) return 'beginner';
    if (years < 3) return 'intermediate';
    if (years < 7) return 'advanced';
    return 'expert';
  }

  /**
   * Calculate completion rate based on extracted data and conversation progress
   */
  private calculateCompletionRate(session: ConversationSession): number {
    const { userRole, currentStep, extractedData } = session;
    const steps = CONVERSATION_STEPS[userRole];
    const stepKeys = Object.values(steps) as string[];
    const currentIndex = stepKeys.indexOf(currentStep as string);

    // Base completion from step progress
    let stepCompletion = currentIndex >= 0 ? (currentIndex / (stepKeys.length - 1)) : 0;

    // Data completeness bonus
    let dataCompletion = 0;
    const requiredFields = userRole === 'EXPERT'
      ? ['skills', 'experience', 'services', 'pricing']
      : ['projectType', 'budget', 'requirements', 'timeline'];

    const completedFields = requiredFields.filter(field =>
      extractedData[field] &&
      (Array.isArray(extractedData[field]) ? extractedData[field].length > 0 : true)
    );

    dataCompletion = completedFields.length / requiredFields.length;

    // Weighted average (70% step progress, 30% data completeness)
    return Math.min((stepCompletion * 0.7) + (dataCompletion * 0.3), 1.0);
  }

  /**
   * Handle conversation completion and profile creation
   */
  private async handleConversationCompletion(session: ConversationSession): Promise<void> {
    try {
      logger.info('Handling conversation completion', {
        sessionId: session.id,
        userRole: session.userRole,
        extractedDataKeys: Object.keys(session.extractedData),
      });

      // Generate final profile data
      const profileData = await this.generateProfileFromConversation(session);

      // Store in extracted data for later use
      session.extractedData.generatedProfile = profileData;

      // Create actual user profile in database
      try {
        const profileResult = await profileCreationService.createProfileFromAI(
          session.userId,
          session.userRole,
          session.extractedData as AIExtractedData,
          session.id
        );

        session.extractedData.createdProfile = profileResult;

        logger.info('User profile created successfully', {
          sessionId: session.id,
          profileId: profileResult.profileId,
          profileType: profileResult.profileType,
        });

      } catch (profileError: any) {
        logger.error('Failed to create user profile', {
          sessionId: session.id,
          error: profileError.message,
        });
        // Continue with completion message even if profile creation fails
      }

      // Add completion message
      const completionMessage: ChatMessage = {
        role: 'assistant',
        content: this.generateCompletionMessage(session),
        timestamp: new Date(),
      };

      session.messages.push(completionMessage);

      logger.info('Conversation completion handled successfully', {
        sessionId: session.id,
        profileGenerated: !!profileData,
        profileCreated: !!session.extractedData.createdProfile,
      });

    } catch (error: any) {
      logger.error('Error handling conversation completion', {
        sessionId: session.id,
        error: error.message,
      });
    }
  }

  /**
   * Generate profile data from conversation
   */
  private async generateProfileFromConversation(session: ConversationSession): Promise<any> {
    const { userRole, extractedData, language } = session;

    if (userRole === 'EXPERT') {
      return {
        type: 'expert_profile',
        personalInfo: {
          skills: extractedData.skills || [],
          experience: extractedData.experience || {},
          specializations: extractedData.specializations || [],
        },
        services: extractedData.services || [],
        pricing: extractedData.pricing || {},
        portfolio: extractedData.portfolio || [],
        availability: extractedData.availability || 'available',
        marketPosition: extractedData.marketPosition || {},
        generatedAt: new Date().toISOString(),
        language,
      };
    } else {
      return {
        type: 'client_profile',
        projectInfo: {
          type: extractedData.projectType || 'general',
          description: extractedData.description || '',
          requirements: extractedData.requirements || [],
        },
        budget: extractedData.budget || {},
        timeline: extractedData.timeline || {},
        preferences: extractedData.preferences || {},
        generatedAt: new Date().toISOString(),
        language,
      };
    }
  }

  /**
   * Generate completion message
   */
  private generateCompletionMessage(session: ConversationSession): string {
    const { userRole, language, extractedData } = session;
    const profileCreated = !!extractedData.createdProfile;

    if (language === 'ar') {
      return userRole === 'EXPERT'
        ? `🎉 **تهانينا!** تم إكمال إعداد ملفك الشخصي بنجاح!

**ما تم إنجازه:**
✅ تحليل مهاراتك وخبراتك المهنية
✅ تحديد خدماتك المميزة والمطلوبة
✅ وضع استراتيجية تسعير تنافسية
✅ إنشاء ملف شخصي احترافي ${profileCreated ? 'وحفظه في النظام' : ''}

**الخطوات التالية:**
🚀 سيتم توجيهك إلى لوحة التحكم الخاصة بك
📝 يمكنك إضافة أعمال سابقة ومعرض أعمال
💼 ابدأ في استقبال طلبات العملاء
📊 راقب إحصائياتك وأرباحك

${profileCreated ? '✨ **ملفك الشخصي جاهز للعرض على العملاء!**' : '⚠️ **سيتم إنشاء ملفك الشخصي في الخطوة التالية**'}

**مرحباً بك في مجتمع الخبراء السوريين المحترفين!** 🇸🇾`
        : `🎉 **تهانينا!** تم إعداد ملفك بنجاح!

**ما تم إنجازه:**
✅ تحديد نوع مشروعك ومتطلباته التقنية
✅ وضع ميزانية وجدول زمني واقعي
✅ تحديد معايير اختيار الخبير المناسب
✅ إعداد ملف العميل ${profileCreated ? 'وحفظه في النظام' : ''}

**الخطوات التالية:**
🔍 سيتم توجيهك لتصفح الخبراء المناسبين
💬 يمكنك التواصل مع الخبراء وطلب عروض أسعار
📋 ابدأ مشروعك مع أفضل الخبراء السوريين
📈 تابع تقدم مشاريعك من لوحة التحكم

${profileCreated ? '✨ **ملفك جاهز لبدء البحث عن الخبراء!**' : '⚠️ **سيتم إنشاء ملفك في الخطوة التالية**'}

**مرحباً بك في منصة فريلا سوريا!** 🇸🇾`;
    }

    // English fallback
    return userRole === 'EXPERT'
      ? `Congratulations! Your expert profile has been successfully created${profileCreated ? ' and saved' : ''}. You will now be redirected to your dashboard.`
      : `Congratulations! Your client profile has been set up${profileCreated ? ' and saved' : ''}. You can now browse and connect with Syrian experts.`;
  }
}

// Export singleton instance
export const aiConversationService = new AIConversationService();
