/**
 * Enhanced AI Chat Screen
 * Integrates Phase 2 UI Components with Phase 3 AI Services
 * Features: Voice recording, image upload, real-time AI conversation
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Context & Store
import { useTheme } from '../../contexts/ThemeContext';
import { authStore } from '../../store/authStore';

// Services
import { 
  phase3AIService, 
  Phase3AIMessage, 
  Phase3AISession,
  ConversationStartResult 
} from '../../services/phase3AIService';
import { voiceRecognitionService } from '../../services/voiceRecognitionService';
import { imageAnalysisService } from '../../services/imageAnalysisService';

// Components
import { 
  VoiceRecordingButton,
  ImageUploadCard,
  EnhancedChatInput,
  AIProcessingIndicator 
} from '../../components/ai';

// Utils
import { showToast } from '../../utils/toast';

const { width, height } = Dimensions.get('window');

interface EnhancedAIChatScreenProps {
  navigation: any;
  route: {
    params?: {
      sessionId?: string;
      userRole?: 'CLIENT' | 'EXPERT';
      language?: 'ar' | 'en';
      sessionType?: 'onboarding' | 'profile_optimization' | 'service_creation';
    };
  };
}

const EnhancedAIChatScreen: React.FC<EnhancedAIChatScreenProps> = ({ navigation, route }) => {
  const { currentTheme } = useTheme();
  const { user } = authStore();
  
  // State
  const [messages, setMessages] = useState<Phase3AIMessage[]>([]);
  const [currentSession, setCurrentSession] = useState<Phase3AISession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingType, setProcessingType] = useState<'voice' | 'image' | 'text' | 'matching'>('text');
  
  // Refs
  const flatListRef = useRef<FlatList>(null);

  // Get params from route
  const {
    sessionId,
    userRole = user?.role as 'CLIENT' | 'EXPERT' || 'CLIENT',
    language = 'ar',
    sessionType = 'onboarding',
  } = route.params || {};

  /**
   * Initialize AI chat session
   */
  useEffect(() => {
    initializeAIChat();
    
    return () => {
      // Cleanup
      voiceRecognitionService.cleanup();
    };
  }, []);

  /**
   * Initialize AI chat connection
   */
  const initializeAIChat = async () => {
    try {
      setIsConnecting(true);
      
      let sessionData: ConversationStartResult;
      
      if (sessionId) {
        // Get existing session
        const session = await phase3AIService.getSession(sessionId);
        const messages = await phase3AIService.getMessages(sessionId);
        
        setCurrentSession(session);
        setMessages(messages);
        
        // Create welcome data structure
        sessionData = {
          sessionId: session.sessionId,
          welcomeMessage: 'مرحباً بعودتك! دعنا نكمل محادثتنا.',
          currentStep: session.currentStep,
          culturalContext: session.culturalContext,
          features: session.features,
          nextSteps: []
        };
      } else {
        // Start new conversation
        sessionData = await phase3AIService.startConversation(
          userRole,
          language,
          sessionType,
          {
            location: '', // Can be enhanced with user location
            dialect: 'general'
          }
        );
        
        // Add welcome message
        const welcomeMessage: Phase3AIMessage = {
          id: `welcome_${Date.now()}`,
          sessionId: sessionData.sessionId,
          role: 'assistant',
          content: sessionData.welcomeMessage,
          messageType: 'text',
          timestamp: new Date()
        };
        
        setMessages([welcomeMessage]);
      }
      
      // Update navigation title
      navigation.setOptions({
        title: getStepTitle(sessionData.currentStep, language),
      });
      
      setIsConnecting(false);
      
    } catch (error: any) {
      console.error('Failed to initialize AI chat:', error);
      setIsConnecting(false);
      showToast('error', 'فشل في بدء المحادثة', 'Failed to start conversation');
      
      Alert.alert(
        'خطأ في الاتصال',
        'فشل في الاتصال بخدمة المحادثة الذكية. يرجى المحاولة مرة أخرى.',
        [
          { text: 'إعادة المحاولة', onPress: initializeAIChat },
          { text: 'إلغاء', onPress: () => navigation.goBack() },
        ]
      );
    }
  };

  /**
   * Send text message
   */
  const sendTextMessage = async (message: string) => {
    if (!message.trim() || isLoading || !currentSession) return;

    const messageText = message.trim();
    setIsLoading(true);

    try {
      // Add user message to UI
      const userMessage: Phase3AIMessage = {
        id: `user_${Date.now()}`,
        sessionId: currentSession.sessionId,
        role: 'user',
        content: messageText,
        messageType: 'text',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, userMessage]);
      scrollToBottom();

      // Send to AI
      const response = await phase3AIService.sendMessage(
        currentSession.sessionId,
        messageText,
        'text'
      );

      if (response.success) {
        setMessages(prev => [...prev, response.data.aiMessage]);
        scrollToBottom();

        // Update session state with enhanced data
        if (currentSession) {
          const updatedSession = {
            ...currentSession,
            currentStep: response.data.currentStep || currentSession.currentStep,
            extractedData: {
              ...currentSession.extractedData,
              ...response.data.extractedData
            },
            completionRate: response.data.completionRate || 0.0
          };
          setCurrentSession(updatedSession);
        }

        // Handle conversation completion
        if (response.data.isCompleted) {
          showToast('success', 'تم إكمال المحادثة بنجاح!', 'Conversation completed successfully!');

          // Navigate to dashboard after a short delay
          setTimeout(() => {
            // Navigate based on user role
            if (currentSession?.userRole === 'EXPERT') {
              // Navigate to expert dashboard
              console.log('Navigating to expert dashboard...');
            } else {
              // Navigate to client dashboard
              console.log('Navigating to client dashboard...');
            }
          }, 2000);
        }
      }
    } catch (error: any) {
      console.error('Failed to send message:', error);
      showToast('error', 'فشل في إرسال الرسالة', 'Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle voice recording
   */
  const handleVoiceRecording = async () => {
    if (!currentSession) return;

    try {
      setIsProcessing(true);
      setProcessingType('voice');

      // Record and process voice with AI
      const result = await voiceRecognitionService.recordAndProcessWithAI(
        currentSession.sessionId,
        {
          maxDuration: 60,
          enableDialectDetection: true,
          enableDataExtraction: true
        }
      );

      // Add voice message to UI
      const voiceMessage: Phase3AIMessage = {
        id: `voice_${Date.now()}`,
        sessionId: currentSession.sessionId,
        role: 'user',
        content: result.transcription,
        messageType: 'voice_transcript',
        metadata: {
          voiceData: {
            transcription: result.transcription,
            confidence: result.confidence,
            dialect: result.dialect,
            duration: 0 // Would need to track actual duration
          }
        },
        timestamp: new Date()
      };

      setMessages(prev => [...prev, voiceMessage]);
      scrollToBottom();

      // Send transcription to AI for response
      const response = await phase3AIService.sendMessage(
        currentSession.sessionId,
        result.transcription,
        'voice_transcript',
        { voiceData: result }
      );

      if (response.success) {
        setMessages(prev => [...prev, response.data.aiMessage]);
        scrollToBottom();
      }

    } catch (error: any) {
      console.error('Failed to process voice:', error);
      showToast('error', 'فشل في معالجة الصوت', 'Failed to process voice');
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * Handle image upload and analysis
   */
  const handleImageUpload = async () => {
    if (!currentSession) return;

    try {
      setIsProcessing(true);
      setProcessingType('image');

      // Pick and analyze image with AI
      const result = await imageAnalysisService.pickAndAnalyzeWithAI(
        currentSession.sessionId,
        'gallery',
        'portfolio'
      );

      if (result) {
        // Add image message to UI
        const imageMessage: Phase3AIMessage = {
          id: `image_${Date.now()}`,
          sessionId: currentSession.sessionId,
          role: 'user',
          content: result.description,
          messageType: 'image_description',
          metadata: {
            imageData: {
              description: result.description,
              analysisType: result.analysisType,
              extractedSkills: result.extractedSkills,
              confidence: result.confidence
            }
          },
          timestamp: new Date()
        };

        setMessages(prev => [...prev, imageMessage]);
        scrollToBottom();

        // Send analysis to AI for response
        const response = await phase3AIService.sendMessage(
          currentSession.sessionId,
          result.description,
          'image_description',
          { imageData: result }
        );

        if (response.success) {
          setMessages(prev => [...prev, response.data.aiMessage]);
          scrollToBottom();
        }
      }

    } catch (error: any) {
      console.error('Failed to process image:', error);
      showToast('error', 'فشل في معالجة الصورة', 'Failed to process image');
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * Scroll to bottom
   */
  const scrollToBottom = () => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  /**
   * Get step title
   */
  const getStepTitle = (step: string, lang: string): string => {
    const titles: Record<string, Record<string, string>> = {
      welcome: { ar: 'مرحباً', en: 'Welcome' },
      personal_info: { ar: 'المعلومات الشخصية', en: 'Personal Info' },
      skills_assessment: { ar: 'تقييم المهارات', en: 'Skills Assessment' },
      experience_review: { ar: 'مراجعة الخبرة', en: 'Experience Review' },
      portfolio_analysis: { ar: 'تحليل المحفظة', en: 'Portfolio Analysis' },
      completion: { ar: 'اكتمل', en: 'Completed' },
    };
    
    return titles[step]?.[lang] || 'AI Assistant';
  };

  if (isConnecting) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: currentTheme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={currentTheme.colors.primary[500]} />
          <Text style={[styles.loadingText, { color: currentTheme.colors.text.primary }]}>
            جاري الاتصال بالمساعد الذكي...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: currentTheme.colors.background }]}>
      <KeyboardAvoidingView 
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Messages List */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={({ item }) => <MessageBubble message={item} theme={currentTheme} />}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={scrollToBottom}
        />

        {/* Processing Indicator */}
        {isProcessing && (
          <View style={styles.processingContainer}>
            <AIProcessingIndicator
              type={processingType}
              message={
                processingType === 'voice' ? 'جاري معالجة الصوت...' :
                processingType === 'image' ? 'جاري تحليل الصورة...' :
                'جاري المعالجة...'
              }
              size="medium"
            />
          </View>
        )}

        {/* Enhanced Chat Input */}
        <View style={styles.inputContainer}>
          <EnhancedChatInput
            onSendMessage={sendTextMessage}
            onVoiceRecord={handleVoiceRecording}
            onImageUpload={handleImageUpload}
            disabled={isLoading || isProcessing}
            isRecording={false} // Would need to track recording state
            isProcessing={isProcessing}
            placeholder="اكتب رسالتك أو استخدم الصوت والصور..."
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// Message Bubble Component
const MessageBubble: React.FC<{ message: Phase3AIMessage; theme: any }> = ({ message, theme }) => {
  const isUser = message.role === 'user';
  const isRTL = true; // Arabic RTL

  return (
    <View style={[
      styles.messageContainer,
      isUser ? styles.userMessageContainer : styles.aiMessageContainer,
    ]}>
      <LinearGradient
        colors={isUser 
          ? [theme.colors.primary[500] + '20', theme.colors.primary[500] + '10']
          : [theme.colors.glass.background, theme.colors.glass.background + '80']
        }
        style={[
          styles.messageBubble,
          isUser ? styles.userBubble : styles.aiBubble,
          { borderColor: theme.colors.glass.border }
        ]}
      >
        {/* Message Type Indicator */}
        {message.messageType !== 'text' && (
          <View style={styles.messageTypeIndicator}>
            <Icon
              name={
                message.messageType === 'voice_transcript' ? 'mic' :
                message.messageType === 'image_description' ? 'image' : 'chat'
              }
              size={14}
              color={theme.colors.primary[500]}
            />
            <Text style={[styles.messageTypeText, { color: theme.colors.text.secondary }]}>
              {message.messageType === 'voice_transcript' ? 'رسالة صوتية' :
               message.messageType === 'image_description' ? 'تحليل صورة' : 'نص'}
            </Text>
          </View>
        )}

        <Text style={[
          styles.messageText,
          { 
            color: theme.colors.text.primary,
            fontFamily: 'Cairo-Regular'
          },
          isRTL && styles.rtlText,
        ]}>
          {message.content}
        </Text>
        
        <Text style={[
          styles.messageTime,
          { color: theme.colors.text.muted },
        ]}>
          {new Date(message.timestamp).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
          })}
        </Text>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Cairo-Regular',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 8,
  },
  messageContainer: {
    marginVertical: 4,
    maxWidth: width * 0.8,
  },
  userMessageContainer: {
    alignSelf: 'flex-end',
  },
  aiMessageContainer: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    padding: 12,
    borderRadius: 16,
    borderWidth: 1,
  },
  userBubble: {
    borderBottomRightRadius: 4,
  },
  aiBubble: {
    borderBottomLeftRadius: 4,
  },
  messageTypeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  messageTypeText: {
    fontSize: 12,
    marginLeft: 4,
    fontFamily: 'Cairo-Regular',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 4,
  },
  rtlText: {
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  messageTime: {
    fontSize: 12,
    textAlign: 'right',
    fontFamily: 'Cairo-Regular',
  },
  processingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  inputContainer: {
    padding: 16,
  },
});

export default EnhancedAIChatScreen;
