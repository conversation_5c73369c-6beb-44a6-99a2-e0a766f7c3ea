/**
 * Enhanced OpenRouter API Integration Service
 * Handles AI model interactions for Freela Syria onboarding system
 * Optimized for Syrian marketplace with Arabic RTL support and cultural context
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';

// OpenRouter API Configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';

// Enhanced AI Models for different use cases with fallback hierarchy
export const AI_MODELS = {
  GPT_4_TURBO: 'openai/gpt-4-turbo-preview',
  GPT_4: 'openai/gpt-4',
  CLAUDE_3_SONNET: 'anthropic/claude-3-sonnet-20240229',
  CLAUDE_3_HAIKU: 'anthropic/claude-3-haiku-20240307',
  GEMINI_PRO: 'google/gemini-pro',
  GPT_3_5_TURBO: 'openai/gpt-3.5-turbo',
} as const;

// Model fallback hierarchy for reliability
export const MODEL_FALLBACK_CHAIN = [
  AI_MODELS.GPT_4_TURBO,
  AI_MODELS.GPT_4,
  AI_MODELS.CLAUDE_3_SONNET,
  AI_MODELS.GPT_3_5_TURBO,
  AI_MODELS.CLAUDE_3_HAIKU,
] as const;

export type AIModel = typeof AI_MODELS[keyof typeof AI_MODELS];

// Message types for conversation
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: Date;
}

export interface AIResponse {
  content: string;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  finish_reason: string;
}

export interface ConversationContext {
  userId: string;
  sessionId: string;
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  currentStep: string;
  extractedData: Record<string, any>;
  culturalContext?: {
    location?: string;
    dialect?: string;
    marketFocus?: string;
  };
  sessionType?: 'onboarding' | 'profile_optimization' | 'service_creation';
}

// Enhanced response interface with confidence scoring
export interface EnhancedAIResponse extends AIResponse {
  confidence: number;
  extractedData?: Record<string, any>;
  nextStep?: string;
  recommendations?: Array<{
    type: string;
    content: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  fallbackUsed?: boolean;
  modelUsed: string;
}

class OpenRouterService {
  private client: AxiosInstance;
  private defaultModel: AIModel = AI_MODELS.GPT_4_TURBO;
  private retryAttempts: number = 3;
  private retryDelay: number = 1000; // 1 second base delay

  constructor() {
    this.client = axios.create({
      baseURL: OPENROUTER_API_URL,
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria AI Onboarding - Syrian Marketplace',
      },
      timeout: 45000, // Increased timeout for better reliability
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.info('OpenRouter API Request', {
          url: config.url,
          method: config.method,
          model: config.data?.model,
        });
        return config;
      },
      (error) => {
        logger.error('OpenRouter API Request Error', { error });
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.info('OpenRouter API Response', {
          status: response.status,
          usage: response.data?.usage,
        });
        return response;
      },
      (error) => {
        logger.error('OpenRouter API Response Error', {
          status: error.response?.status,
          message: error.response?.data?.error?.message,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Enhanced chat completion with fallback models and retry logic
   */
  async chatCompletion(
    messages: ChatMessage[],
    context: Partial<ConversationContext> = {},
    options: {
      model?: AIModel;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): Promise<AIResponse> {
    const {
      model = this.defaultModel,
      temperature = 0.7,
      maxTokens = 1000,
      stream = false,
    } = options;

    // Try primary model first, then fallback models
    const modelsToTry = model === this.defaultModel
      ? MODEL_FALLBACK_CHAIN
      : [model, ...MODEL_FALLBACK_CHAIN.filter(m => m !== model)];

    let lastError: any;

    for (let i = 0; i < modelsToTry.length; i++) {
      const currentModel = modelsToTry[i];

      try {
        const result = await this.attemptChatCompletion(
          messages,
          context,
          {
            model: currentModel,
            temperature,
            maxTokens,
            stream,
          }
        );

        // Log successful model usage
        if (i > 0) {
          logger.info('Fallback model used successfully', {
            originalModel: model,
            usedModel: currentModel,
            fallbackIndex: i,
          });
        }

        return result;

      } catch (error: any) {
        lastError = error;
        logger.warn(`Model ${currentModel} failed, trying next fallback`, {
          error: error.message,
          modelIndex: i,
          remainingModels: modelsToTry.length - i - 1,
        });

        // If this is a rate limit error, wait before trying next model
        if (error.response?.status === 429 && i < modelsToTry.length - 1) {
          await this.delay(this.retryDelay * (i + 1));
        }
      }
    }

    // All models failed, throw the last error
    throw this.handleChatCompletionError(lastError, context, messages.length);
  }

  /**
   * Attempt chat completion with a specific model
   */
  private async attemptChatCompletion(
    messages: ChatMessage[],
    context: Partial<ConversationContext>,
    options: {
      model: AIModel;
      temperature: number;
      maxTokens: number;
      stream: boolean;
    }
  ): Promise<AIResponse> {
    // Add enhanced system context for Arabic/Syrian market
    const fullMessages = context.userRole && context.language
      ? [this.buildEnhancedSystemMessage(context as ConversationContext), ...messages]
      : messages;

    const response: AxiosResponse = await this.client.post('/chat/completions', {
      model: options.model,
      messages: fullMessages,
      temperature: options.temperature,
      max_tokens: options.maxTokens,
      stream: options.stream,
      top_p: 0.9,
      frequency_penalty: 0.1,
      presence_penalty: 0.1,
    });

    const choice = response.data.choices[0];

    return {
      content: choice.message.content,
      model: response.data.model,
      usage: response.data.usage,
      finish_reason: choice.finish_reason,
    };
  }

  /**
   * Handle chat completion errors with proper error types
   */
  private handleChatCompletionError(error: any, context: any, messageCount: number): Error {
    logger.error('All OpenRouter models failed', {
      error: error.message,
      context,
      messageCount,
    });

    if (error.response?.status === 429) {
      return createError.tooManyRequests(
        'AI service rate limit exceeded. Please try again later.',
        'تم تجاوز حد الاستخدام لخدمة الذكاء الاصطناعي. يرجى المحاولة مرة أخرى لاحقاً.'
      );
    } else if (error.response?.status === 401) {
      return createError.unauthorized(
        'AI service authentication failed.',
        'فشل في مصادقة خدمة الذكاء الاصطناعي.'
      );
    } else if (error.response?.status >= 500) {
      return createError.internalServerError(
        'AI service temporarily unavailable.',
        'خدمة الذكاء الاصطناعي غير متاحة مؤقتاً.'
      );
    } else {
      return createError.badRequest(
        'Failed to process AI request.',
        'فشل في معالجة طلب الذكاء الاصطناعي.'
      );
    }
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Build enhanced system message with comprehensive Syrian market context
   */
  private buildEnhancedSystemMessage(context: ConversationContext): ChatMessage {
    const { userRole, language, currentStep, culturalContext, sessionType } = context;
    
    const baseContext = culturalContext || { location: 'Syria', dialect: 'Syrian', marketFocus: 'local_and_regional' };
    const sessionContext = sessionType || 'onboarding';

    const systemPrompts = {
      ar: {
        CLIENT: `أنت مساعد ذكي متخصص لمنصة فريلا سوريا، منصة العمل الحر الرائدة في سوريا والمنطقة العربية. مهمتك مساعدة العملاء في إعداد ملفاتهم الشخصية وإيجاد أفضل الخبراء لمشاريعهم.

🇸🇾 السياق الثقافي السوري:
- تفهم الثقافة السورية العريقة والتحديات المعاصرة
- تدعم اللغة العربية بطلاقة مع فهم عميق للهجة السورية ومصطلحاتها
- تراعي الظروف الاقتصادية والاجتماعية الحالية في سوريا
- تقدر قيم العمل الجماعي والاحترام المتبادل في الثقافة السورية
- تفهم أهمية العلاقات الشخصية في بيئة الأعمال السورية

💼 خبرة السوق المحلي:
- تعرف احتياجات السوق السوري والعربي
- تفهم التحديات التقنية والمالية المحلية
- تقدم حلول عملية ومناسبة للميزانيات المحلية
- تراعي أوقات العمل والعطل الرسمية في سوريا

🎯 مهامك الأساسية:
1. فهم احتياجات العميل ونوع المشروع بدقة
2. استخراج المعلومات المهمة (الميزانية، الجدول الزمني، المتطلبات التقنية)
3. اقتراح فئات الخدمات المناسبة للسوق السوري
4. تقديم نصائح لكتابة وصف مشروع فعال وجذاب
5. مساعدة في تحديد الأولويات والمراحل الزمنية

📋 نوع الجلسة: ${sessionContext}
📍 الموقع: ${baseContext.location}
🗣️ اللهجة: ${baseContext.dialect}

كن ودودًا ومفيدًا ومهنيًا في تفاعلك، واستخدم أمثلة من السوق السوري عند الحاجة.`,

        EXPERT: `أنت مساعد ذكي متخصص لمنصة فريلا سوريا، منصة العمل الحر الرائدة في سوريا والمنطقة العربية. مهمتك مساعدة الخبراء السوريين في بناء ملفات مهنية قوية وإنشاء خدمات مميزة تجذب العملاء.

🇸🇾 السياق الثقافي السوري:
- تفهم سوق العمل السوري والمهارات المطلوبة محلياً وإقليمياً
- تدعم اللغة العربية بطلاقة مع فهم عميق للهجة السورية
- تراعي التحديات المهنية والاقتصادية في سوريا
- تقدر الخبرات السورية وتساعد في إبرازها بشكل احترافي
- تفهم أهمية بناء السمعة المهنية في المجتمع السوري

💼 خبرة السوق والتسعير:
- تعرف متوسط الأسعار في السوق السوري والعربي
- تفهم المنافسة المحلية والإقليمية
- تقدم استراتيجيات تسويق مناسبة للسوق المحلي والعربي
- تساعد في تحديد نقاط القوة التنافسية

🎯 مهامك الأساسية:
1. استخراج المهارات والخبرات المهنية بدقة
2. تحسين وصف الملف الشخصي ليكون جذاباً ومهنياً
3. اقتراح خدمات مربحة ومطلوبة في السوق
4. تحديد الأسعار المناسبة والتنافسية
5. تقديم نصائح لبناء سمعة مهنية قوية
6. مساعدة في كتابة أوصاف خدمات مقنعة

📋 نوع الجلسة: ${sessionContext}
📍 الموقع: ${baseContext.location}
🗣️ اللهجة: ${baseContext.dialect}
🎯 التركيز: ${baseContext.marketFocus}

كن محفزًا ومشجعًا ومهنيًا في تفاعلك، واستخدم أمثلة من نجاحات الخبراء السوريين عند الحاجة.`
      },
      en: {
        CLIENT: `You are an AI assistant for Freela Syria, the leading freelance marketplace in Syria. Your mission is to help clients set up their profiles and find the right experts for their projects.

Syrian Context:
- Understand Syrian culture and local challenges
- Support Arabic language fluently with Syrian dialect understanding
- Consider economic and social conditions in Syria
- Provide practical solutions suitable for the Syrian market

Your tasks:
1. Understand client needs and project type
2. Extract important information (budget, timeline, requirements)
3. Suggest appropriate service categories
4. Provide tips for writing effective project descriptions

Be friendly, helpful, and professional in your interactions.`,

        EXPERT: `You are an AI assistant for Freela Syria, the leading freelance marketplace in Syria. Your mission is to help experts set up their professional profiles and services professionally.

Syrian Context:
- Understand the Syrian job market and required skills
- Support Arabic language fluently with Syrian dialect understanding
- Consider professional challenges in Syria
- Provide marketing strategies suitable for local and Arab markets

Your tasks:
1. Extract professional skills and experiences
2. Improve profile descriptions
3. Suggest profitable and in-demand services
4. Determine appropriate market pricing
5. Provide tips for building a strong professional reputation

Be motivating, encouraging, and professional in your interactions.`
      }
    };

    return {
      role: 'system',
      content: systemPrompts[language][userRole],
      timestamp: new Date(),
    };
  }

  /**
   * Enhanced conversation method for profile creation with data extraction
   */
  async enhancedConversation(
    messages: ChatMessage[],
    context: ConversationContext,
    options: {
      model?: AIModel;
      temperature?: number;
      maxTokens?: number;
      extractData?: boolean;
    } = {}
  ): Promise<EnhancedAIResponse> {
    const {
      model = this.defaultModel,
      temperature = 0.7,
      maxTokens = 1200,
      extractData = true,
    } = options;

    try {
      // Get AI response
      const aiResponse = await this.chatCompletion(messages, context, {
        model,
        temperature,
        maxTokens,
      });

      // Extract structured data if requested
      let extractedData: Record<string, any> = {};
      let confidence = 0.8;
      let nextStep = context.currentStep;
      let recommendations: Array<{ type: string; content: string; priority: 'high' | 'medium' | 'low' }> = [];

      if (extractData && messages.length > 0) {
        const lastUserMessage = messages.filter(m => m.role === 'user').pop();
        if (lastUserMessage) {
          const extractionResult = await this.extractDataFromMessage(
            lastUserMessage.content,
            context
          );
          extractedData = extractionResult.data;
          confidence = extractionResult.confidence;
          nextStep = extractionResult.nextStep;
          recommendations = extractionResult.recommendations;
        }
      }

      return {
        ...aiResponse,
        confidence,
        extractedData,
        nextStep,
        recommendations,
        fallbackUsed: false,
        modelUsed: aiResponse.model,
      };

    } catch (error: any) {
      logger.error('Enhanced conversation failed', {
        error: error.message,
        context: context.sessionId,
        messagesCount: messages.length,
      });
      throw error;
    }
  }

  /**
   * Extract structured data from user message using AI
   */
  private async extractDataFromMessage(
    userMessage: string,
    context: ConversationContext
  ): Promise<{
    data: Record<string, any>;
    confidence: number;
    nextStep: string;
    recommendations: Array<{ type: string; content: string; priority: 'high' | 'medium' | 'low' }>;
  }> {
    const extractionPrompt = this.buildDataExtractionPrompt(userMessage, context);

    try {
      const extractionResponse = await this.chatCompletion(
        [{ role: 'user', content: extractionPrompt, timestamp: new Date() }],
        { ...context, currentStep: 'data_extraction' },
        {
          model: AI_MODELS.GPT_4_TURBO,
          temperature: 0.3,
          maxTokens: 800,
        }
      );

      const parsed = JSON.parse(extractionResponse.content);
      return {
        data: parsed.extractedData || {},
        confidence: parsed.confidence || 0.5,
        nextStep: parsed.nextStep || context.currentStep,
        recommendations: parsed.recommendations || [],
      };

    } catch (error: any) {
      logger.warn('Data extraction failed, using fallback', {
        error: error.message,
        userMessage: userMessage.substring(0, 100),
      });

      return {
        data: { rawText: userMessage },
        confidence: 0.3,
        nextStep: context.currentStep,
        recommendations: [],
      };
    }
  }

  /**
   * Build data extraction prompt
   */
  private buildDataExtractionPrompt(userMessage: string, context: ConversationContext): string {
    const { userRole, language, currentStep } = context;

    return `
استخرج البيانات المهمة من رسالة المستخدم التالية وقدم النتيجة في تنسيق JSON:

رسالة المستخدم: "${userMessage}"
نوع المستخدم: ${userRole}
الخطوة الحالية: ${currentStep}
اللغة: ${language}

المطلوب استخراجه:
${userRole === 'EXPERT' ? `
- المهارات والخبرات
- سنوات الخبرة
- نوع الخدمات
- الأسعار المقترحة
- المشاريع السابقة
` : `
- نوع المشروع
- الميزانية المتوقعة
- الجدول الزمني
- المتطلبات التقنية
- التفضيلات الخاصة
`}

تنسيق الإجابة:
{
  "extractedData": {
    // البيانات المستخرجة هنا
  },
  "confidence": 0.8,
  "nextStep": "اسم_الخطوة_التالية",
  "recommendations": [
    {
      "type": "نوع_التوصية",
      "content": "محتوى_التوصية",
      "priority": "high|medium|low"
    }
  ]
}
`;
  }

  /**
   * Generate a simple response (compatibility method)
   */
  async generateResponse(
    prompt: string,
    options: {
      model?: AIModel | string;
      temperature?: number;
      maxTokens?: number;
      max_tokens?: number;
    } = {}
  ): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'user',
        content: prompt,
        timestamp: new Date(),
      }
    ];

    const context: ConversationContext = {
      userId: 'system',
      sessionId: 'system',
      userRole: 'CLIENT',
      language: 'ar',
      currentStep: 'general',
      extractedData: {},
    };

    // Handle both maxTokens and max_tokens for backward compatibility
    const chatOptions = {
      model: options.model as AIModel,
      temperature: options.temperature,
      maxTokens: options.maxTokens || options.max_tokens,
    };

    const response = await this.chatCompletion(messages, context, chatOptions);
    return response.content;
  }

  /**
   * Test API connectivity and model availability with enhanced diagnostics
   */
  async testConnection(): Promise<{ success: boolean; details: any }> {
    try {
      const startTime = Date.now();
      const response = await this.client.get('/models');
      const responseTime = Date.now() - startTime;

      const details = {
        modelsCount: response.data?.data?.length || 0,
        responseTime,
        availableModels: MODEL_FALLBACK_CHAIN.filter(model =>
          response.data?.data?.some((m: any) => m.id === model)
        ),
        timestamp: new Date().toISOString(),
      };

      logger.info('OpenRouter connection test successful', details);
      return { success: true, details };
    } catch (error: any) {
      const details = {
        error: error.message,
        status: error.response?.status,
        timestamp: new Date().toISOString(),
      };
      logger.error('OpenRouter connection test failed', details);
      return { success: false, details };
    }
  }

  /**
   * Get available models with filtering for supported models
   */
  async getAvailableModels(): Promise<any[]> {
    try {
      const response = await this.client.get('/models');
      const allModels = response.data?.data || [];

      // Filter to only include models we support
      const supportedModels = allModels.filter((model: any) =>
        Object.values(AI_MODELS).includes(model.id)
      );

      logger.info('Available models fetched', {
        total: allModels.length,
        supported: supportedModels.length,
      });

      return supportedModels;
    } catch (error: any) {
      logger.error('Failed to fetch available models', { error: error.message });
      return [];
    }
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: any;
  }> {
    try {
      const connectionTest = await this.testConnection();

      if (!connectionTest.success) {
        return {
          status: 'unhealthy',
          details: {
            ...connectionTest.details,
            message: 'Cannot connect to OpenRouter API',
          },
        };
      }

      const { responseTime, availableModels } = connectionTest.details;

      if (responseTime > 10000 || availableModels.length === 0) {
        return {
          status: 'degraded',
          details: {
            ...connectionTest.details,
            message: 'Service is slow or limited model availability',
          },
        };
      }

      return {
        status: 'healthy',
        details: {
          ...connectionTest.details,
          message: 'All systems operational',
        },
      };

    } catch (error: any) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
          message: 'Health check failed',
        },
      };
    }
  }
}

// Export singleton instance
export const openRouterService = new OpenRouterService();

// Export class for type imports
export { OpenRouterService };
