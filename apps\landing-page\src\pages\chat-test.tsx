import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@/themes';

// Mock data types
interface MockAIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  confidence?: number;
}

interface MockSession {
  id: string;
  currentStep: string;
  status: string;
  userRole: 'CLIENT' | 'EXPERT';
  messages: MockAIMessage[];
  extractedData: any;
  completionRate: number;
}

// Mock data
const mockMessages: MockAIMessage[] = [
  {
    id: '1',
    role: 'assistant',
    content: 'مرحباً بك في فريلا سوريا! أنا هنا لمساعدتك في إعداد ملفك الشخصي. هل أنت خبير تريد تقديم خدماتك أم عميل تبحث عن خدمات؟',
    timestamp: new Date(Date.now() - 300000).toISOString(),
    confidence: 0.95
  },
  {
    id: '2',
    role: 'user',
    content: 'أنا خبير في تطوير المواقع الإلكترونية',
    timestamp: new Date(Date.now() - 240000).toISOString()
  },
  {
    id: '3',
    role: 'assistant',
    content: 'ممتاز! تطوير المواقع الإلكترونية مجال مطلوب جداً. يمكنك أن تخبرني أكثر عن خبراتك؟ مثلاً، ما هي التقنيات التي تتقنها؟ React، Vue، Angular؟',
    timestamp: new Date(Date.now() - 180000).toISOString(),
    confidence: 0.92
  }
];

const mockSession: MockSession = {
  id: 'test-session-1',
  currentStep: 'skills_assessment',
  status: 'active',
  userRole: 'EXPERT',
  messages: mockMessages,
  extractedData: {},
  completionRate: 0.3
};

// Message Bubble Component
const MessageBubble: React.FC<{ message: MockAIMessage; isLast: boolean }> = ({ message, isLast }) => {
  const isUser = message.role === 'user';
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
    >
      <div className={`flex items-end space-x-2 rtl:space-x-reverse max-w-[80%]`}>
        {!isUser && (
          <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
            </svg>
          </div>
        )}

        {/* Message Content */}
        <div
          className={`
            px-4 py-3 rounded-2xl shadow-sm backdrop-blur-sm
            ${isUser
              ? 'bg-primary-500 text-white rounded-br-md'
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600'
            }
          `}
        >
          <p className="text-sm leading-relaxed whitespace-pre-wrap font-cairo">
            {message.content}
          </p>
          
          {/* Timestamp */}
          <div className={`text-xs mt-2 ${isUser ? 'text-primary-100' : 'text-gray-500 dark:text-gray-400'}`}>
            {new Date(message.timestamp).toLocaleTimeString('ar-SA', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>

        {isUser && (
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Typing Indicator Component
const TypingIndicator: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="flex justify-start mb-4"
    >
      <div className="flex items-end space-x-2 rtl:space-x-reverse">
        <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
          </svg>
        </div>
        <div className="bg-white dark:bg-gray-700 rounded-2xl rounded-bl-md px-4 py-3 border border-gray-200 dark:border-gray-600">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Main Chat Test Component
const ChatTestPage: React.FC = () => {
  const { currentTheme } = useTheme();
  const [messages, setMessages] = useState<MockAIMessage[]>(mockSession.messages);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Mock send message function
  const sendMessage = async () => {
    if (!inputValue.trim() || isSending) return;

    const userMessage: MockAIMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsSending(true);
    setIsTyping(true);

    // Simulate AI response delay
    setTimeout(() => {
      const aiResponses = [
        'شكراً لك على هذه المعلومات المفيدة!',
        'هذا رائع! يمكنك أن تخبرني أكثر عن خبراتك؟',
        'ممتاز! ما هي المشاريع التي عملت عليها مؤخراً؟',
        'أفهم. هل لديك أمثلة على أعمالك السابقة؟',
        'رائع! كم سنة من الخبرة لديك في هذا المجال؟'
      ];

      const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];
      
      const aiMessage: MockAIMessage = {
        id: `ai_${Date.now()}`,
        role: 'assistant',
        content: randomResponse,
        timestamp: new Date().toISOString(),
        confidence: Math.random() * 0.3 + 0.7 // Random confidence between 0.7-1.0
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
      setIsSending(false);
    }, 2000);
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo" dir="rtl">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            اختبار واجهة الدردشة الذكية
          </h1>
          <p className="text-gray-300">
            اختبار مستقل لواجهة الدردشة مع الذكاء الاصطناعي - فريلا سوريا
          </p>
        </div>

        {/* Chat Container */}
        <div className="glass-theme rounded-2xl overflow-hidden shadow-theme-lg max-w-2xl mx-auto">
          {/* Chat Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-500/10 to-primary-600/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">مساعد فريلا الذكي</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">متصل الآن</p>
                </div>
              </div>
              
              {/* Progress indicator */}
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {Math.round(mockSession.completionRate * 100)}% مكتمل
              </div>
            </div>
          </div>

          {/* Messages Area */}
          <div className="h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
            {messages.map((message, index) => (
              <MessageBubble
                key={message.id}
                message={message}
                isLast={index === messages.length - 1}
              />
            ))}
            
            <AnimatePresence>
              {isTyping && <TypingIndicator />}
            </AnimatePresence>
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-end space-x-3 rtl:space-x-reverse">
              <div className="flex-1">
                <textarea
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="اكتب رسالتك هنا..."
                  className="
                    w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600
                    bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                    focus:ring-2 focus:ring-primary-500 focus:border-transparent
                    resize-none max-h-32 font-cairo
                    placeholder-gray-500 dark:placeholder-gray-400
                  "
                  rows={1}
                  disabled={isSending}
                />
              </div>
              
              <button
                onClick={sendMessage}
                disabled={!inputValue.trim() || isSending}
                className="
                  p-3 bg-primary-500 text-white rounded-xl
                  hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed
                  transition-colors duration-200
                  flex-shrink-0
                "
              >
                {isSending ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="mt-8 text-center">
          <div className="glass-theme rounded-xl p-4 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-white mb-3">عناصر التحكم في الاختبار</h3>
            <div className="space-y-2">
              <button
                onClick={() => setIsTyping(!isTyping)}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                {isTyping ? 'إيقاف' : 'تشغيل'} مؤشر الكتابة
              </button>
              <button
                onClick={() => setMessages(mockSession.messages)}
                className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                إعادة تعيين الرسائل
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatTestPage;
