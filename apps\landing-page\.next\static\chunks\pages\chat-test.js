/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/chat-test"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/chat-test\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/chat-test.tsx */ \"./src/pages/chat-test.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/chat-test\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9QyUzQSU1Q1VzZXJzJTVDYW1lcmslNUNEb2N1bWVudHMlNUNGcmVlbGElNUNhcHBzJTVDbGFuZGluZy1wYWdlJTVDc3JjJTVDcGFnZXMlNUNjaGF0LXRlc3QudHN4JnBhZ2U9JTJGY2hhdC10ZXN0ISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLDREQUEyQjtBQUNsRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZGU4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2NoYXQtdGVzdFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2NoYXQtdGVzdC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2NoYXQtdGVzdFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!\n"));

/***/ }),

/***/ "./src/pages/chat-test.tsx":
/*!*********************************!*\
  !*** ./src/pages/chat-test.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/themes */ \"./src/themes/index.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n// Mock data\nconst mockMessages = [\n    {\n        id: \"1\",\n        role: \"assistant\",\n        content: \"مرحباً بك في فريلا سوريا! أنا هنا لمساعدتك في إعداد ملفك الشخصي. هل أنت خبير تريد تقديم خدماتك أم عميل تبحث عن خدمات؟\",\n        timestamp: new Date(Date.now() - 300000).toISOString(),\n        confidence: 0.95\n    },\n    {\n        id: \"2\",\n        role: \"user\",\n        content: \"أنا خبير في تطوير المواقع الإلكترونية\",\n        timestamp: new Date(Date.now() - 240000).toISOString()\n    },\n    {\n        id: \"3\",\n        role: \"assistant\",\n        content: \"ممتاز! تطوير المواقع الإلكترونية مجال مطلوب جداً. يمكنك أن تخبرني أكثر عن خبراتك؟ مثلاً، ما هي التقنيات التي تتقنها؟ React، Vue، Angular؟\",\n        timestamp: new Date(Date.now() - 180000).toISOString(),\n        confidence: 0.92\n    }\n];\nconst mockSession = {\n    id: \"test-session-1\",\n    currentStep: \"skills_assessment\",\n    status: \"active\",\n    userRole: \"EXPERT\",\n    messages: mockMessages,\n    extractedData: {},\n    completionRate: 0.3\n};\n// Message Bubble Component\nconst MessageBubble = (param)=>{\n    let { message, isLast } = param;\n    const isUser = message.role === \"user\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        className: \"flex \".concat(isUser ? \"justify-end\" : \"justify-start\", \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse max-w-[80%]\",\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            px-4 py-3 rounded-2xl shadow-sm backdrop-blur-sm\\n            \".concat(isUser ? \"bg-primary-500 text-white rounded-br-md\" : \"bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600\", \"\\n          \"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm leading-relaxed whitespace-pre-wrap font-cairo\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs mt-2 \".concat(isUser ? \"text-primary-100\" : \"text-gray-500 dark:text-gray-400\"),\n                            children: new Date(message.timestamp).toLocaleTimeString(\"ar-SA\", {\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MessageBubble;\n// Typing Indicator Component\nconst TypingIndicator = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 10\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -10\n        },\n        className: \"flex justify-start mb-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-end space-x-2 rtl:space-x-reverse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-700 rounded-2xl rounded-bl-md px-4 py-3 border border-gray-200 dark:border-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0.2s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TypingIndicator;\n// Main Chat Test Component\nconst ChatTestPage = ()=>{\n    _s();\n    const { currentTheme } = (0,_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockSession.messages);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isTyping\n    ]);\n    // Focus input on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _inputRef_current;\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    }, []);\n    // Mock send message function\n    const sendMessage = async ()=>{\n        if (!inputValue.trim() || isSending) return;\n        const userMessage = {\n            id: \"user_\".concat(Date.now()),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsSending(true);\n        setIsTyping(true);\n        // Simulate AI response delay\n        setTimeout(()=>{\n            const aiResponses = [\n                \"شكراً لك على هذه المعلومات المفيدة!\",\n                \"هذا رائع! يمكنك أن تخبرني أكثر عن خبراتك؟\",\n                \"ممتاز! ما هي المشاريع التي عملت عليها مؤخراً؟\",\n                \"أفهم. هل لديك أمثلة على أعمالك السابقة؟\",\n                \"رائع! كم سنة من الخبرة لديك في هذا المجال؟\"\n            ];\n            const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];\n            const aiMessage = {\n                id: \"ai_\".concat(Date.now()),\n                role: \"assistant\",\n                content: randomResponse,\n                timestamp: new Date().toISOString(),\n                confidence: Math.random() * 0.3 + 0.7 // Random confidence between 0.7-1.0\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n            setIsTyping(false);\n            setIsSending(false);\n        }, 2000);\n    };\n    // Handle Enter key press\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4 font-cairo\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-white mb-2\",\n                            children: \"اختبار واجهة الدردشة الذكية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: \"اختبار مستقل لواجهة الدردشة مع الذكاء الاصطناعي - فريلا سوريا\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glass-theme rounded-2xl overflow-hidden shadow-theme-lg max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-500/10 to-primary-600/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"مساعد فريلا الذكي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"متصل الآن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            Math.round(mockSession.completionRate * 100),\n                                            \"% مكتمل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900\",\n                            children: [\n                                messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBubble, {\n                                        message: message,\n                                        isLast: index === messages.length - 1\n                                    }, message.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                                    children: isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 28\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end space-x-3 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: inputRef,\n                                            value: inputValue,\n                                            onChange: (e)=>setInputValue(e.target.value),\n                                            onKeyPress: handleKeyPress,\n                                            placeholder: \"اكتب رسالتك هنا...\",\n                                            className: \" w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none max-h-32 font-cairo placeholder-gray-500 dark:placeholder-gray-400 \",\n                                            rows: 1,\n                                            disabled: isSending\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: sendMessage,\n                                        disabled: !inputValue.trim() || isSending,\n                                        className: \" p-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex-shrink-0 \",\n                                        children: isSending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-theme rounded-xl p-4 max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-3\",\n                                children: \"عناصر التحكم في الاختبار\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsTyping(!isTyping),\n                                        className: \"w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\",\n                                        children: [\n                                            isTyping ? \"إيقاف\" : \"تشغيل\",\n                                            \" مؤشر الكتابة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMessages(mockSession.messages),\n                                        className: \"w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\",\n                                        children: \"إعادة تعيين الرسائل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\chat-test.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatTestPage, \"k/QYIh+o0K/Anpxvw10Rq4lKn/s=\", false, function() {\n    return [\n        _themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c2 = ChatTestPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatTestPage);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MessageBubble\");\n$RefreshReg$(_c1, \"TypingIndicator\");\n$RefreshReg$(_c2, \"ChatTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/chat-test.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Camerk%5CDocuments%5CFreela%5Capps%5Clanding-page%5Csrc%5Cpages%5Cchat-test.tsx&page=%2Fchat-test!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);