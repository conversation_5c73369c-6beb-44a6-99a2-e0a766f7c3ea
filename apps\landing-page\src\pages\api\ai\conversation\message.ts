import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

interface SendMessageRequest {
  sessionId: string;
  message: string;
  messageType: 'text' | 'voice' | 'image';
  metadata?: any;
}

interface SendMessageResponse {
  success: boolean;
  message: string;
  data?: {
    aiMessage: {
      id: string;
      role: 'assistant';
      content: string;
      timestamp: string;
      confidence?: number;
      extractedData?: any;
    };
    extractedData: any;
    isCompleted: boolean;
    nextStep?: string;
    completionRate: number;
  };
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SendMessageResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get user session
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.id) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { sessionId, message, messageType, metadata }: SendMessageRequest = req.body;

    // Validate required fields
    if (!sessionId || !message) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: sessionId, message'
      });
    }

    // Forward request to the enhanced main API with proper authentication
    const apiResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3005'}/api/v1/ai/conversation/${sessionId}/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.user.id}`, // Use user ID as token for now
        'X-User-ID': session.user.id, // Additional user identification
        'X-Cultural-Context': JSON.stringify({
          location: 'Syria',
          dialect: 'Syrian',
          marketFocus: 'local_and_regional'
        }),
      },
      body: JSON.stringify({
        message,
        messageType: messageType || 'text',
        metadata: metadata || {}
      }),
    });

    if (!apiResponse.ok) {
      const errorData = await apiResponse.json().catch(() => ({}));
      throw new Error(errorData.message || `API request failed with status ${apiResponse.status}`);
    }

    const apiData = await apiResponse.json();

    if (!apiData.success) {
      throw new Error(apiData.message || 'Failed to send message');
    }

    // Return enhanced response with proper data structure
    return res.status(200).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        aiMessage: {
          id: apiData.data.aiResponse?.id || `ai_${Date.now()}`,
          role: 'assistant',
          content: apiData.data.aiResponse?.content || 'شكراً لك على رسالتك. سأقوم بمعالجتها والرد عليك قريباً.',
          timestamp: apiData.data.aiResponse?.timestamp || new Date().toISOString(),
          confidence: apiData.data.aiResponse?.confidence || 0.8,
          extractedData: apiData.data.extractedData || {}
        },
        extractedData: apiData.data.extractedData || {},
        isCompleted: apiData.data.isCompleted || apiData.data.currentStep === 'completion',
        nextStep: apiData.data.currentStep,
        completionRate: apiData.data.completionRate || 0.0,
        recommendations: apiData.data.recommendations || [],
        culturalContext: apiData.data.culturalContext || {
          location: 'Syria',
          dialect: 'Syrian',
          marketFocus: 'local_and_regional'
        }
      }
    });

  } catch (error: any) {
    console.error('Error sending message:', error);
    
    // Return a fallback AI response in case of API failure
    return res.status(200).json({
      success: true,
      message: 'Message processed with fallback response',
      data: {
        aiMessage: {
          id: `fallback_${Date.now()}`,
          role: 'assistant',
          content: generateFallbackResponse(req.body.message),
          timestamp: new Date().toISOString(),
          confidence: 0.5
        },
        extractedData: {},
        isCompleted: false,
        completionRate: 0.1
      }
    });
  }
}

/**
 * Generate a fallback response when the main API is unavailable
 */
function generateFallbackResponse(userMessage: string): string {
  const message = userMessage.toLowerCase();
  
  // Simple keyword-based responses
  if (message.includes('مهارة') || message.includes('خبرة') || message.includes('تخصص')) {
    return 'ممتاز! أخبرني المزيد عن مهاراتك وخبراتك في هذا المجال. كم سنة من الخبرة لديك؟';
  }
  
  if (message.includes('مشروع') || message.includes('عمل') || message.includes('خدمة')) {
    return 'رائع! هذا مجال مهم جداً. ما هي أنواع المشاريع التي عملت عليها من قبل؟';
  }
  
  if (message.includes('سعر') || message.includes('تكلفة') || message.includes('مال')) {
    return 'بالنسبة للأسعار، من المهم أن تكون تنافسية ومناسبة للسوق السوري. ما هو متوسط السعر الذي تفكر فيه؟';
  }
  
  if (message.includes('عميل') || message.includes('زبون')) {
    return 'التعامل مع العملاء مهارة مهمة. أخبرني عن تجربتك في التواصل مع العملاء وإدارة المشاريع.';
  }
  
  // Default responses
  const defaultResponses = [
    'شكراً لك على هذه المعلومات المفيدة. أخبرني المزيد عن ذلك.',
    'هذا مثير للاهتمام! كيف يمكنني مساعدتك أكثر في هذا الموضوع؟',
    'أفهم ما تقصده. هل يمكنك إعطائي المزيد من التفاصيل؟',
    'ممتاز! دعنا نتعمق أكثر في هذا الموضوع.',
    'أقدر مشاركتك لهذه المعلومات. ما هي الخطوة التالية التي تفكر فيها؟'
  ];
  
  return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
}
