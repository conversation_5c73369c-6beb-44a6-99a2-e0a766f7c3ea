/**
 * Phase 3 AI Service for Mobile App
 * Enhanced AI conversation service with OpenRouter integration
 * Connects mobile app to backend Phase 3 AI endpoints
 */

import { apiRequest } from './api';
import { showToast } from '../utils/toast';

export interface Phase3AISession {
  sessionId: string;
  userRole: 'CLIENT' | 'EXPERT';
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation' | 'skill_assessment';
  language: 'ar' | 'en';
  currentStep: string;
  status: 'active' | 'completed' | 'paused' | 'abandoned';
  culturalContext: {
    country: string;
    language: string;
    dialect: string;
    location: string;
    marketContext: string;
    economicContext: string;
    culturalNotes: string[];
  };
  features: {
    realTimeProcessing: boolean;
    skillExtraction: boolean;
    marketIntelligence: boolean;
    culturalAdaptation: boolean;
    voiceSupport: boolean;
    imageAnalysis: boolean;
  };
  extractedData: any;
  completionRate: number;
  createdAt: Date;
  lastActivity: Date;
}

export interface Phase3AIMessage {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant';
  content: string;
  messageType: 'text' | 'voice_transcript' | 'image_description';
  metadata?: {
    voiceData?: {
      transcription: string;
      confidence: number;
      dialect: string;
      duration: number;
    };
    imageData?: {
      description: string;
      analysisType: 'portfolio' | 'work_sample' | 'skill_demonstration' | 'certificate';
      extractedSkills: string[];
      confidence: number;
    };
    processingTime?: number;
    aiModel?: string;
  };
  timestamp: Date;
}

export interface Phase3AIResponse {
  success: boolean;
  message: string;
  data: {
    sessionId: string;
    aiMessage: Phase3AIMessage;
    currentStep: string;
    extractedData: any;
    isCompleted: boolean;
    nextSteps?: string[];
    suggestions?: string[];
  };
}

export interface VoiceProcessingResult {
  transcription: string;
  confidence: number;
  dialect: string;
  extractedData: any;
  processingTime: number;
}

export interface ImageAnalysisResult {
  description: string;
  analysisType: string;
  extractedSkills: string[];
  confidence: number;
  suggestions: string[];
  marketRelevance: number;
}

export interface ConversationStartResult {
  sessionId: string;
  welcomeMessage: string;
  currentStep: string;
  culturalContext: any;
  features: any;
  nextSteps: string[];
}

class Phase3AIService {
  private baseUrl = '/ai'; // Updated to match enhanced API endpoints
  
  /**
   * Start a new Phase 3 AI conversation
   */
  async startConversation(
    userRole: 'CLIENT' | 'EXPERT',
    language: 'ar' | 'en' = 'ar',
    sessionType: string = 'onboarding',
    culturalContext: any = {}
  ): Promise<ConversationStartResult> {
    try {
      console.log('🚀 Starting Phase 3 AI conversation:', {
        userRole,
        language,
        sessionType,
        culturalContext
      });

      const response = await apiRequest.post(`${this.baseUrl}/conversation/start`, {
        userRole,
        language,
        sessionType,
        culturalContext: {
          location: culturalContext.location || 'Syria',
          dialect: culturalContext.dialect || 'Syrian',
          marketFocus: 'local_and_regional',
          ...culturalContext
        }
      });

      if (response.success) {
        console.log('✅ Phase 3 AI conversation started:', response.data.sessionId);
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to start conversation');
      }
    } catch (error: any) {
      console.error('❌ Failed to start Phase 3 AI conversation:', error);
      showToast('error', 'فشل في بدء المحادثة', 'Failed to start conversation');
      throw error;
    }
  }

  /**
   * Send message to AI conversation
   */
  async sendMessage(
    sessionId: string,
    message: string,
    messageType: 'text' | 'voice_transcript' | 'image_description' = 'text',
    metadata?: any
  ): Promise<Phase3AIResponse> {
    try {
      console.log('📤 Sending message to Phase 3 AI:', {
        sessionId,
        messageType,
        messageLength: message.length
      });

      const response = await apiRequest.post(`${this.baseUrl}/conversation/${sessionId}/message`, {
        message,
        messageType,
        metadata
      });

      if (response.success) {
        console.log('✅ Message sent successfully');
        return response;
      } else {
        throw new Error(response.message || 'Failed to send message');
      }
    } catch (error: any) {
      console.error('❌ Failed to send message:', error);
      showToast('error', 'فشل في إرسال الرسالة', 'Failed to send message');
      throw error;
    }
  }

  /**
   * Process voice input
   */
  async processVoice(
    sessionId: string,
    audioUri: string,
    dialect: string = 'general'
  ): Promise<VoiceProcessingResult> {
    try {
      console.log('🎤 Processing voice input:', { sessionId, dialect });

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('audio', {
        uri: audioUri,
        type: 'audio/wav',
        name: 'voice_recording.wav',
      } as any);
      formData.append('dialect', dialect);

      const response = await fetch(`${this.baseUrl}/conversation/${sessionId}/voice`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Voice processed successfully');
        return result.data;
      } else {
        throw new Error(result.message || 'Failed to process voice');
      }
    } catch (error: any) {
      console.error('❌ Failed to process voice:', error);
      showToast('error', 'فشل في معالجة الصوت', 'Failed to process voice');
      throw error;
    }
  }

  /**
   * Analyze image
   */
  async analyzeImage(
    sessionId: string,
    imageUri: string,
    analysisType: 'portfolio' | 'work_sample' | 'skill_demonstration' | 'certificate' = 'portfolio'
  ): Promise<ImageAnalysisResult> {
    try {
      console.log('🖼️ Analyzing image:', { sessionId, analysisType });

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'image_upload.jpg',
      } as any);
      formData.append('analysisType', analysisType);

      const response = await fetch(`${this.baseUrl}/conversation/${sessionId}/image`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Image analyzed successfully');
        return result.data;
      } else {
        throw new Error(result.message || 'Failed to analyze image');
      }
    } catch (error: any) {
      console.error('❌ Failed to analyze image:', error);
      showToast('error', 'فشل في تحليل الصورة', 'Failed to analyze image');
      throw error;
    }
  }

  /**
   * Get session details
   */
  async getSession(sessionId: string): Promise<Phase3AISession> {
    try {
      const response = await apiRequest.get(`${this.baseUrl}/conversation/${sessionId}`);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get session');
      }
    } catch (error: any) {
      console.error('Failed to get session:', error);
      throw error;
    }
  }

  /**
   * Get conversation messages
   */
  async getMessages(sessionId: string): Promise<Phase3AIMessage[]> {
    try {
      const response = await apiRequest.get(`${this.baseUrl}/conversation/${sessionId}/messages`);

      if (response.success) {
        return response.data.messages;
      } else {
        throw new Error(response.message || 'Failed to get messages');
      }
    } catch (error: any) {
      console.error('Failed to get messages:', error);
      throw error;
    }
  }

  /**
   * Complete conversation
   */
  async completeConversation(sessionId: string): Promise<{
    extractedData: any;
    profileSummary: string;
    recommendations: string[];
    nextSteps: string[];
  }> {
    try {
      const response = await apiRequest.post(`${this.baseUrl}/conversation/${sessionId}/complete`);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to complete conversation');
      }
    } catch (error: any) {
      console.error('Failed to complete conversation:', error);
      showToast('error', 'فشل في إكمال المحادثة', 'Failed to complete conversation');
      throw error;
    }
  }

  /**
   * Get user's conversation sessions
   */
  async getUserSessions(): Promise<Phase3AISession[]> {
    try {
      const response = await apiRequest.get(`${this.baseUrl}/conversations`);

      if (response.success) {
        return response.data.sessions;
      } else {
        throw new Error(response.message || 'Failed to get user sessions');
      }
    } catch (error: any) {
      console.error('Failed to get user sessions:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const phase3AIService = new Phase3AIService();
