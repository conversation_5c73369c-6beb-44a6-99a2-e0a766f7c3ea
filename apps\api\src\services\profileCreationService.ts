/**
 * Profile Creation Service
 * Handles automatic profile creation from AI conversation data
 * Integrates with Supabase database and maintains Arabic RTL support
 */

import { supabaseAdmin } from '@freela/database/src/supabase';
import { logger } from '../utils/logger';
import { createError } from '../utils/errors';

export interface AIExtractedData {
  // Expert data
  skills?: string[];
  experience?: {
    years: number;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    projects?: string[];
  };
  services?: Array<{
    title: { ar: string; en: string };
    description: { ar: string; en: string };
    pricing: { basic: number; standard: number; premium: number };
    deliveryTime: number;
  }>;
  pricing?: {
    hourlyRate: number;
    currency: string;
  };
  portfolio?: Array<{
    title: string;
    description: string;
    imageUrl?: string;
  }>;
  availability?: string;
  
  // Client data
  projectType?: string;
  budget?: {
    amount: number;
    currency: string;
  };
  timeline?: {
    duration: number;
    unit: 'days' | 'weeks' | 'months';
  };
  requirements?: string[];
  preferences?: {
    expertLevel?: string;
    location?: string;
    language?: string;
  };
  
  // Common data
  location?: {
    governorate: string;
    city: string;
  };
  language?: string;
  culturalContext?: {
    location: string;
    dialect: string;
    marketFocus: string;
  };
}

export interface ProfileCreationResult {
  success: boolean;
  profileId: string;
  profileType: 'expert' | 'client';
  data: any;
  message: string;
}

class ProfileCreationService {
  /**
   * Create profile from AI conversation data
   */
  async createProfileFromAI(
    userId: string,
    userRole: 'CLIENT' | 'EXPERT',
    extractedData: AIExtractedData,
    sessionId: string
  ): Promise<ProfileCreationResult> {
    try {
      logger.info('Creating profile from AI data', {
        userId,
        userRole,
        sessionId,
        extractedDataKeys: Object.keys(extractedData),
      });

      if (userRole === 'EXPERT') {
        return await this.createExpertProfile(userId, extractedData, sessionId);
      } else {
        return await this.createClientProfile(userId, extractedData, sessionId);
      }

    } catch (error: any) {
      logger.error('Failed to create profile from AI data', {
        userId,
        userRole,
        sessionId,
        error: error.message,
      });
      throw createError.internalServerError('Failed to create profile');
    }
  }

  /**
   * Create expert profile from AI data
   */
  private async createExpertProfile(
    userId: string,
    extractedData: AIExtractedData,
    sessionId: string
  ): Promise<ProfileCreationResult> {
    // Check if expert profile already exists
    const { data: existingProfile } = await supabaseAdmin
      .from('expert_profiles')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingProfile) {
      throw createError.badRequest('Expert profile already exists');
    }

    // Prepare expert profile data
    const profileData = {
      user_id: userId,
      title: this.generateTitle(extractedData, 'expert'),
      description: this.generateDescription(extractedData, 'expert'),
      skills: extractedData.skills || [],
      experience: this.mapExperienceLevel(extractedData.experience?.level || 'beginner'),
      hourly_rate: extractedData.pricing?.hourlyRate || null,
      availability: this.generateAvailability(extractedData),
      response_time: 'WITHIN_24_HOURS',
      completed_projects: 0,
      rating: 0,
      review_count: 0,
      verified: false,
      verification_documents: [],
    };

    // Create expert profile
    const { data: profile, error } = await supabaseAdmin
      .from('expert_profiles')
      .insert(profileData)
      .select()
      .single();

    if (error) {
      logger.error('Failed to create expert profile', { error, userId });
      throw createError.databaseError('Failed to create expert profile');
    }

    // Create services if provided
    if (extractedData.services && extractedData.services.length > 0) {
      await this.createServicesForExpert(profile.id, extractedData.services);
    }

    // Update user onboarding status
    await this.updateUserOnboardingStatus(userId);

    logger.info('Expert profile created successfully', {
      userId,
      profileId: profile.id,
      sessionId,
    });

    return {
      success: true,
      profileId: profile.id,
      profileType: 'expert',
      data: profile,
      message: 'Expert profile created successfully',
    };
  }

  /**
   * Create client profile from AI data
   */
  private async createClientProfile(
    userId: string,
    extractedData: AIExtractedData,
    sessionId: string
  ): Promise<ProfileCreationResult> {
    // Check if client profile already exists
    const { data: existingProfile } = await supabaseAdmin
      .from('client_profiles')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingProfile) {
      throw createError.badRequest('Client profile already exists');
    }

    // Prepare client profile data
    const profileData = {
      user_id: userId,
      company_name: null, // Will be updated later if business account
      company_size: null,
      industry: this.extractIndustryFromProjectType(extractedData.projectType),
      projects_posted: 0,
      total_spent: 0,
      rating: 0,
      review_count: 0,
    };

    // Create client profile
    const { data: profile, error } = await supabaseAdmin
      .from('client_profiles')
      .insert(profileData)
      .select()
      .single();

    if (error) {
      logger.error('Failed to create client profile', { error, userId });
      throw createError.databaseError('Failed to create client profile');
    }

    // Update user onboarding status
    await this.updateUserOnboardingStatus(userId);

    logger.info('Client profile created successfully', {
      userId,
      profileId: profile.id,
      sessionId,
    });

    return {
      success: true,
      profileId: profile.id,
      profileType: 'client',
      data: profile,
      message: 'Client profile created successfully',
    };
  }

  /**
   * Generate localized title from extracted data
   */
  private generateTitle(extractedData: AIExtractedData, type: 'expert' | 'client'): any {
    if (type === 'expert') {
      const primarySkill = extractedData.skills?.[0] || 'خبير';
      return {
        ar: `خبير ${primarySkill}`,
        en: `${primarySkill} Expert`,
      };
    } else {
      return {
        ar: 'عميل في فريلا سوريا',
        en: 'Freela Syria Client',
      };
    }
  }

  /**
   * Generate localized description from extracted data
   */
  private generateDescription(extractedData: AIExtractedData, type: 'expert' | 'client'): any {
    if (type === 'expert') {
      const skills = extractedData.skills?.join('، ') || 'مهارات متنوعة';
      const experience = extractedData.experience?.years || 0;
      
      return {
        ar: `خبير متخصص في ${skills} مع ${experience} سنوات من الخبرة. أقدم خدمات عالية الجودة للعملاء في السوق السوري والعربي.`,
        en: `Specialized expert in ${skills} with ${experience} years of experience. Providing high-quality services to clients in the Syrian and Arab markets.`,
      };
    } else {
      return {
        ar: 'عميل يبحث عن خبراء محترفين لتنفيذ مشاريع عالية الجودة.',
        en: 'Client looking for professional experts to execute high-quality projects.',
      };
    }
  }

  /**
   * Map experience level to database enum
   */
  private mapExperienceLevel(level: string): string {
    const mapping: Record<string, string> = {
      'beginner': 'BEGINNER',
      'intermediate': 'INTERMEDIATE',
      'advanced': 'ADVANCED',
      'expert': 'EXPERT',
    };
    return mapping[level] || 'BEGINNER';
  }

  /**
   * Generate availability object
   */
  private generateAvailability(extractedData: AIExtractedData): any {
    return {
      timezone: 'Asia/Damascus',
      workingHours: {
        monday: { start: '09:00', end: '17:00', available: true },
        tuesday: { start: '09:00', end: '17:00', available: true },
        wednesday: { start: '09:00', end: '17:00', available: true },
        thursday: { start: '09:00', end: '17:00', available: true },
        friday: { start: '09:00', end: '17:00', available: true },
        saturday: { start: '09:00', end: '17:00', available: false },
        sunday: { start: '09:00', end: '17:00', available: false },
      },
      status: extractedData.availability || 'available',
    };
  }

  /**
   * Extract industry from project type
   */
  private extractIndustryFromProjectType(projectType?: string): string | null {
    if (!projectType) return null;
    
    const industryMapping: Record<string, string> = {
      'design': 'Design & Creative',
      'programming': 'Technology',
      'writing': 'Content & Writing',
      'translation': 'Translation & Languages',
      'marketing': 'Marketing & Sales',
      'business': 'Business & Consulting',
    };
    
    return industryMapping[projectType] || 'Other';
  }

  /**
   * Create services for expert
   */
  private async createServicesForExpert(expertId: string, services: any[]): Promise<void> {
    const serviceData = services.map(service => ({
      expert_id: expertId,
      title: service.title,
      description: service.description,
      category: 'general',
      pricing: service.pricing,
      delivery_time: service.deliveryTime || 7,
      status: 'ACTIVE',
      features: [],
    }));

    const { error } = await supabaseAdmin
      .from('services')
      .insert(serviceData);

    if (error) {
      logger.error('Failed to create services for expert', { error, expertId });
    }
  }

  /**
   * Update user onboarding status
   */
  private async updateUserOnboardingStatus(userId: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('users')
      .update({ 
        has_completed_onboarding: true,
        data_collected: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (error) {
      logger.error('Failed to update user onboarding status', { error, userId });
    }
  }
}

// Export singleton instance
export const profileCreationService = new ProfileCreationService();
